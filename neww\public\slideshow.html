<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Power of You - Video Slideshow</title>
    <link rel="stylesheet" href="slideshow.css">
</head>
<body>
    <div class="slideshow-container">
        <!-- Header -->
        <div class="slideshow-header">
            <h1>Power of You</h1>
            <div class="slide-counter">
                <span class="current-slide">1</span> / <span class="total-slides">3</span>
            </div>
        </div>

        <!-- Slides Container -->
        <div class="slides-wrapper">
            <!-- Slide 1 - Portada -->
            <div class="slide active" data-slide="1">
                <div class="slide-content presentation-slide">
                    <div class="slide-header">
                        <div class="logo">
                            <div class="logo-icon">🦅</div>
                            <div class="logo-text">
                                <div class="logo-main">ABILITY</div>
                                <div class="logo-sub">SEMINARS</div>
                            </div>
                        </div>
                        <h1 class="main-title">The Power of You</h1>
                    </div>

                    <div class="slide-body">
                        <h2 class="subtitle">The product of this seminar is a more powerful you.</h2>

                        <div class="people-section">
                            <div class="person-pair left-pair">
                                <div class="person man">
                                    <div class="person-image man-image"></div>
                                </div>
                                <div class="person woman pointing">
                                    <div class="person-image woman-image"></div>
                                </div>
                            </div>

                            <div class="divider-line"></div>

                            <div class="person-pair right-pair">
                                <div class="person man">
                                    <div class="person-image man-image"></div>
                                </div>
                                <div class="person woman confident">
                                    <div class="person-image woman-image"></div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="slide-footer">
                        <div class="copyright">©2025 Ability Seminars Group. All Rights Reserved.</div>
                        <div class="attribution">Based on the works of L. Ron Hubbard</div>
                    </div>
                </div>
            </div>

            <!-- Slide 2 -->
            <div class="slide" data-slide="2">
                <div class="slide-content">
                    <video controls preload="metadata" poster="">
                        <source src="video2.mp4" type="video/mp4">
                        Tu navegador no soporta el elemento video.
                    </video>
                    <div class="slide-info">
                        <h2>Video 2</h2>
                        <p>Descripción del segundo video de la presentación.</p>
                    </div>
                </div>
            </div>

            <!-- Slide 3 -->
            <div class="slide" data-slide="3">
                <div class="slide-content">
                    <video controls preload="metadata" poster="">
                        <source src="video3.mp4" type="video/mp4">
                        Tu navegador no soporta el elemento video.
                    </video>
                    <div class="slide-info">
                        <h2>Video 3</h2>
                        <p>Descripción del tercer video de la presentación.</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Navigation Controls -->
        <div class="navigation">
            <button class="nav-btn prev-btn" onclick="changeSlide(-1)">
                <span>&#8249;</span>
            </button>
            <button class="nav-btn next-btn" onclick="changeSlide(1)">
                <span>&#8250;</span>
            </button>
        </div>

        <!-- Slide Indicators -->
        <div class="indicators">
            <span class="indicator active" onclick="currentSlide(1)"></span>
            <span class="indicator" onclick="currentSlide(2)"></span>
            <span class="indicator" onclick="currentSlide(3)"></span>
        </div>

        <!-- Controls -->
        <div class="controls">
            <button class="control-btn" onclick="playAllVideos()">
                <span>▶</span> Reproducir Todo
            </button>
            <button class="control-btn" onclick="pauseAllVideos()">
                <span>⏸</span> Pausar Todo
            </button>
            <button class="control-btn" onclick="toggleFullscreen()">
                <span>⛶</span> Pantalla Completa
            </button>
        </div>
    </div>

    <script src="slideshow.js"></script>
</body>
</html>
