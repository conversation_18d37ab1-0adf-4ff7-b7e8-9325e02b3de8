/* Reset y base */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Arial', sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 20px;
}

/* Container principal */
.slideshow-container {
    max-width: 1200px;
    width: 100%;
    background: white;
    border-radius: 20px;
    box-shadow: 0 20px 40px rgba(0,0,0,0.1);
    overflow: hidden;
    position: relative;
}

/* Header */
.slideshow-header {
    background: linear-gradient(45deg, #667eea, #764ba2);
    color: white;
    padding: 20px 30px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.slideshow-header h1 {
    font-size: 2.5rem;
    font-weight: bold;
    text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
}

.slide-counter {
    font-size: 1.2rem;
    background: rgba(255,255,255,0.2);
    padding: 8px 16px;
    border-radius: 20px;
    backdrop-filter: blur(10px);
}

/* Slides wrapper */
.slides-wrapper {
    position: relative;
    height: 600px;
    overflow: hidden;
}

/* Individual slides */
.slide {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    opacity: 0;
    transform: translateX(100%);
    transition: all 0.5s ease-in-out;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 40px;
}

.slide.active {
    opacity: 1;
    transform: translateX(0);
}

.slide.prev {
    transform: translateX(-100%);
}

/* Slide content */
.slide-content {
    width: 100%;
    max-width: 800px;
    text-align: center;
}

.slide-content video {
    width: 100%;
    max-height: 400px;
    border-radius: 15px;
    box-shadow: 0 10px 30px rgba(0,0,0,0.2);
    margin-bottom: 20px;
}

.slide-info h2 {
    font-size: 2rem;
    color: #333;
    margin-bottom: 10px;
}

.slide-info p {
    font-size: 1.1rem;
    color: #666;
    line-height: 1.6;
}

/* Presentation slide styles */
.presentation-slide {
    max-width: 1000px;
    background: white;
    padding: 40px;
    border-radius: 0;
    height: 100%;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
}

.slide-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 30px;
}

.logo {
    display: flex;
    align-items: center;
    gap: 10px;
}

.logo-icon {
    font-size: 2.5rem;
    color: #1a365d;
}

.logo-text {
    text-align: left;
}

.logo-main {
    font-size: 1.5rem;
    font-weight: bold;
    color: #1a365d;
    line-height: 1;
}

.logo-sub {
    font-size: 1rem;
    color: #1a365d;
    line-height: 1;
}

.main-title {
    font-size: 3.5rem;
    font-weight: bold;
    color: #1a365d;
    text-align: center;
    flex: 1;
    margin: 0 40px;
}

.slide-body {
    flex: 1;
    display: flex;
    flex-direction: column;
    justify-content: center;
}

.subtitle {
    font-size: 2rem;
    color: #1a365d;
    margin-bottom: 40px;
    font-weight: normal;
}

.people-section {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 40px;
    margin: 40px 0;
}

.person-pair {
    display: flex;
    align-items: center;
    gap: 20px;
}

.person {
    position: relative;
}

.person-image {
    width: 120px;
    height: 200px;
    border-radius: 10px;
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
}

.man-image {
    background: linear-gradient(135deg, #4a90e2 0%, #357abd 100%);
    position: relative;
}

.man-image::before {
    content: '👨‍💼';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    font-size: 4rem;
}

.woman-image {
    background: linear-gradient(135deg, #e74c3c 0%, #c0392b 100%);
    position: relative;
}

.woman-image::before {
    content: '👩‍💼';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    font-size: 4rem;
}

.pointing .woman-image::after {
    content: '👉';
    position: absolute;
    right: -30px;
    top: 30%;
    font-size: 2rem;
}

.confident .woman-image::after {
    content: '👍';
    position: absolute;
    right: -30px;
    top: 30%;
    font-size: 2rem;
}

.divider-line {
    width: 3px;
    height: 200px;
    background: #333;
    border-radius: 2px;
}

.slide-footer {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: 30px;
    font-size: 0.9rem;
    color: #666;
}

.attribution {
    background: #4a90e2;
    color: white;
    padding: 8px 16px;
    border-radius: 5px;
    font-weight: bold;
}

/* Navigation buttons */
.navigation {
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    width: 100%;
    display: flex;
    justify-content: space-between;
    padding: 0 20px;
    pointer-events: none;
}

.nav-btn {
    background: rgba(255,255,255,0.9);
    border: none;
    width: 50px;
    height: 50px;
    border-radius: 50%;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 24px;
    color: #333;
    box-shadow: 0 4px 15px rgba(0,0,0,0.2);
    transition: all 0.3s ease;
    pointer-events: all;
}

.nav-btn:hover {
    background: white;
    transform: scale(1.1);
    box-shadow: 0 6px 20px rgba(0,0,0,0.3);
}

.nav-btn:active {
    transform: scale(0.95);
}

/* Indicators */
.indicators {
    display: flex;
    justify-content: center;
    padding: 20px;
    gap: 10px;
}

.indicator {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    background: #ddd;
    cursor: pointer;
    transition: all 0.3s ease;
}

.indicator.active {
    background: #667eea;
    transform: scale(1.2);
}

.indicator:hover {
    background: #764ba2;
}

/* Controls */
.controls {
    display: flex;
    justify-content: center;
    gap: 15px;
    padding: 20px;
    background: #f8f9fa;
}

.control-btn {
    background: linear-gradient(45deg, #667eea, #764ba2);
    color: white;
    border: none;
    padding: 12px 20px;
    border-radius: 25px;
    cursor: pointer;
    font-size: 14px;
    font-weight: bold;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 8px;
}

.control-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
}

.control-btn:active {
    transform: translateY(0);
}

/* Responsive */
@media (max-width: 768px) {
    .slideshow-header {
        padding: 15px 20px;
        flex-direction: column;
        gap: 10px;
    }
    
    .slideshow-header h1 {
        font-size: 2rem;
    }
    
    .slides-wrapper {
        height: 500px;
    }
    
    .slide {
        padding: 20px;
    }
    
    .slide-content video {
        max-height: 300px;
    }
    
    .slide-info h2 {
        font-size: 1.5rem;
    }
    
    .controls {
        flex-wrap: wrap;
        gap: 10px;
    }
    
    .control-btn {
        padding: 10px 15px;
        font-size: 12px;
    }
}

@media (max-width: 480px) {
    body {
        padding: 10px;
    }
    
    .slideshow-container {
        border-radius: 15px;
    }
    
    .slides-wrapper {
        height: 400px;
    }
    
    .slide-content video {
        max-height: 250px;
    }
    
    .nav-btn {
        width: 40px;
        height: 40px;
        font-size: 20px;
    }
}
